2+2
2+2
2+2
a<-2+4
install.packages("ggplot2")
library(ggplot2)
install.packages("ggplot2")
install.packages("ggplot2")
"""
测试时间：2024年05月13日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
"""
测试时间：2024年05月13日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
library(tidyverse)
install.packages("tidyverse")
library(tidyverse)
install.packages("lubridate")
library(tidyverse)
library(ggtext)
install.packages("ggtext")
library(ggtext)
install.packages("hrbrthemes")
library(hrbrthemes)
install.packages("ggsci")
library(ggsci)
install.packages("ggpubr")
install.packages("ggprism")
library(ggprism)
install.packages("readxl")
library(readxl)
library(ggpubr)
library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
#读取数据
heat_data_01 <- read_excel("\\第4章 双变量图形的绘制\\热力图数据01.xlsx")
#读取数据
heat_data_01 <- read_excel("\\第4章 双变量图形的绘制\\热力图数据01.xlsx")
1+1
library(ggplot2)
install.packages(c("ggplot2", "dplyr", "readr", "scales", "RColorBrewer"))
plot_mvtr_bar("results.csv")
library(readr)
library(scales)
source("plot_mvtr_bar.R")
"""
测试时间：2024年05月08日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
breaks <- seq(0,1.5,by = 0.1)
####################################图3-1-1（a）使用ggplot2包的geom_histogram() 函数 绘制的直方图
ggplot() +
geom_histogram(data = hist_data,aes(hist_data),breaks = breaks,
fill="gray",colour="black",linewidth=0.3) +
scale_y_continuous(expand = c(0, 0), limits = c(0, 2500),
breaks = seq(0, 2500, by = 500)) +
scale_x_continuous(breaks = breaks) +
theme_classic()+
theme(
text = element_text(family = "serif",size = 18),
axis.text = element_text(colour = "black"),
axis.ticks.length=unit(.2, "cm"),
axis.ticks = element_line(linewidth = .4),
axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.png",
width = 5.5, height = 4, dpi = 900,device=png)
install.packages("ggsave")
library(ggplot2)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.pdf",
width = 5.5, height = 4,device = cairo_pdf)
setwd("D:/PhD/Code/R/R语言科研论文配图绘制指南配套资料20240820/第3章 单变量图表绘制")
library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggridges)
#读取数据
group_data <- readr::read_csv("\\第3章 单变量图表绘制\\山脊图数据.csv")
group_data <- readr::read_csv("\\第3章 单变量图表绘制\\山脊图数据.csv")
group_data <- readr::read_csv("山脊图数据.csv")
library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
library(ggridges)
#读取数据
group_data <- readr::read_csv("山脊图数据.csv")
####################################图3-2-3（a）使用ggridges包绘制的“山脊”图示例1
ggplot(group_data, aes(x = depth, y = color)) +
geom_density_ridges(size=.8) +
scale_y_discrete(expand = c(0.01, 0)) +
scale_x_continuous(expand = c(0.01, 0)) +
theme_minimal(base_family = "serif",base_size = 17) +
theme(plot.margin = margin(10, 10, 10, 10))
ggsave(filename = "\\第3章 单变量图表绘制\\图3-2-3 使用ggridges包绘制的“山脊”图示例_a.png",
width = 5, height = 5, bg="white",dpi = 900,device=png)
library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(readxl)
dens_data <- readr::read_csv("density_data.csv")
den_long <- pivot_longer(dens_data,cols = starts_with("d"),names_to = "type",values_to = "values")
####################################图3-2-2（a）ggplot2 多子图密度图绘制示例1
ggplot(data = desi_data_long,aes(x = values)) +
geom_density(fill="#DB3132",alpha=0.6,linewidth=0.3) +
geom_rug(length = unit(0.07, "npc")) +
xlim(c(0,25)) +
scale_y_continuous(expand = c(0, 0),limits = c(0,0.15),
breaks = seq(0,0.15,0.05)) +
facet_wrap(~type) +
theme_bw() +
#   hrbrthemes::theme_ipsum_pub(base_family = "serif",
#                               base_size = 12,
#                               plot_margin = margin(10, 10, 10, 10))+
theme(strip.background = element_blank(),
strip.text = element_text(size = 14),
text = element_text(family = "serif",face='bold',size = 16),
axis.text = element_text(colour = "black",face='bold',size = 12),
axis.ticks.length=unit(.2, "cm"),
axis.ticks = element_line(linewidth = .4),
axis.line = element_line(linewidth = .4))
"""
测试时间：2024年05月08日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
"""
测试时间：2024年05月08日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
