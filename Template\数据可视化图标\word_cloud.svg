<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 16.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="130px" height="130px" viewBox="0 0 130 130" enable-background="new 0 0 130 130" xml:space="preserve">
<g>
	<circle fill="#2980DE" cx="65" cy="64.999" r="64"/>
	<g>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M48.761,28.771
			c0.775,0.26,1.426,0.627,2.149,0.931c0.476-0.146,0.539-0.652,0.859-0.931c0.845,0.325,1.237,1.04,2.365,1.118
			c0.172,0.646-0.238,0.788-0.429,1.12c2.693,1.581,5.545,3.026,8.598,4.291c0.78,0.181,0.181-0.836,0.86-0.746
			c1.152,0.121,1.693,0.77,2.581,1.119c-0.768,1.761-1.947,3.164-3.441,4.293c-4.852-0.516-7.295-3.121-10.963-4.667
			c-0.761-0.1-0.528,0.663-1.289,0.561c-0.669-0.352-1.194-0.827-2.15-0.932c-0.06-0.427,0.052-0.703,0.431-0.746
			c-0.368-0.679-1.256-0.902-1.936-1.308C46.437,31.579,48.023,30.077,48.761,28.771z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M39.087,29.331
			c0,5.1,0,10.201,0,15.301c-1.577,0-3.153,0-4.729,0c-0.003-4.317-0.003-11.174,0-15.491
			C35.887,29.246,37.909,28.922,39.087,29.331z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M78.642,35.673
			c0.073,0.769,1.097,1.094,0.215,1.68c0.732,1.479,1.873,2.603,2.578,4.106c0.343-0.017,0.821,0.088,0.862-0.187
			c0.406,0.455,0.959,0.784,0.857,1.678c-1.223,0.309-1.813,1.162-3.653,0.933c-1.349-1.502-2.439-3.232-3.44-5.037
			c-2.1-0.022-0.966-1.755-1.72-2.799c1.107-0.379,2.908-1.967,3.44-0.188C77.974,35.716,78.267,35.661,78.642,35.673z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M61.658,42.018
			c-1.062,1.085-1.498,3.653-3.224,3.547c0.312,2.098-1.123,4.383-4.085,4.104c-1.418-0.133-4.22-1.581-4.73-2.424
			c-0.792-1.312,0.137-2.332,0-4.106c-0.909,0.889-1.551,2.013-2.149,3.171c-5.581-0.705-5.185-7.17-1.505-9.518
			C50.629,33.82,57.853,41.121,61.658,42.018z M46.827,40.527c0.458,1.092,1.831,1.395,3.009,1.865
			C49.815,41.009,48.044,40.259,46.827,40.527z M56.285,45.005c-0.885-1.099-2.506-1.558-3.87-2.24
			C52.75,44.205,54.977,45.61,56.285,45.005z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M67.679,41.272
			c1.717,2.054,3.193,4.32,4.514,6.717c-0.375,0.921-1.627,1.078-2.58,1.493c-1.455-1.848-2.438-4.104-4.084-5.783
			c0.47,1.894,2.406,4.129,3.654,5.97c-0.771,0.515-1.482,1.078-2.58,1.307c-2.258-1.834-3.171-4.836-5.159-6.907
			c0.801-0.797,1.846-1.38,3.224-1.678C65.214,41.622,65.957,41.023,67.679,41.272z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M105.083,50.603
			c-0.41,0.295-0.756,0.417-1.506,0.746c-0.338,0.148-1.188,0.199-1.504,0.374c-1.071,0.588-0.804,1.69-2.796,1.492
			c-2.241-0.223-3.938-4.153-1.289-5.41c-1.085,0.114-1.73,0.612-2.365,1.119c-3.653-2.339,1.313-7.555,4.944-5.225
			C102.345,44.838,103.731,49.018,105.083,50.603z M98.633,47.245c-0.059-0.945-0.354-1.685-1.29-1.867
			C97.095,46.06,97.644,47.077,98.633,47.245z M100.998,50.79c0.217-0.69-0.596-1.925-1.506-2.239
			C99.479,49.744,100.013,50.463,100.998,50.79z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M55.21,51.535
			c-0.757,1.335-1.488,2.691-2.579,3.732c-4.39-1.477-7.37-4.176-12.039-5.411c2.869,2.923,7.789,4.062,11.394,6.345
			c-0.534,1.588-1.714,2.617-2.366,4.106c-5.937-2.81-11.798-5.685-17.412-8.773c0.506-1.486,1.447-2.599,2.365-3.73
			c1.604,0.1,2.317,0.973,3.44,1.492c1.115-6.725,6.655-3.27,10.963-1.119C51.391,49.381,53.755,50.408,55.21,51.535z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M83.371,50.603
			c-1.095,0.977-2.639,1.566-4.085,2.239c-0.862-0.371-0.718-1.617-1.72-1.865c-1.028,3.802,4.635,1.358,7.309,2.238
			c1.396,0.461,3.452,2.942,3.439,4.852c-0.032,4.762-10.291,7.138-11.822,1.308c1.453-0.542,2.327-1.588,4.084-1.868
			c1.211,0.256,1.025,1.725,2.148,2.055c1.4-0.566-0.179-1.964-0.858-2.427c-6.209,0.958-10.527-1.519-9.674-5.599
			C72.965,47.842,82.844,45.436,83.371,50.603z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M66.603,70.57
			c-1.318,1.093-3.033,1.846-4.944,2.425c-1.019-1.825-2.255-3.658-3.438-5.413c-1.156-1.711-2.022-3.702-3.87-4.85
			c1.013,3.819,4.749,7.054,6.45,11.009c-1.813,0.605-2.962,1.785-4.945,2.24c-2.696-4.316-5.534-8.508-8.384-12.689
			c1.094-1.183,3.608-2.854,5.375-2.24c0.308-1.387,1.935-2.974,4.944-2.613C61.677,61.66,63.77,66.437,66.603,70.57z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M45.537,63.477
			c-2.008,0.434-3.118,1.651-4.945,2.242c-0.254-0.901-1.036-1.343-1.289-2.242C41.146,62.063,45.296,59.213,45.537,63.477z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M88.314,62.359
			c5.924-0.802,7.096,2.166,7.096,7.649c0,2.269,0.413,5.13-0.432,6.906c-1.463,3.079-9.206,3.396-10.748,0
			c-0.915-2.02-0.881-9.99,0-12.131C84.884,63.198,86.367,62.623,88.314,62.359z M89.174,65.158
			c0.049,0.354-0.241,0.413-0.214,0.747c0.395,3.328-0.812,8.048,0.645,10.452c1.134-1.072,0.645-3.514,0.645-5.601
			c0-2.085,0.489-4.526-0.645-5.598C89.462,65.158,89.317,65.158,89.174,65.158z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M96.914,62.544
			c1.646,0,3.295,0,4.943,0c0,1.059,0,2.115,0,3.175c1.587-0.479,4.543-0.71,5.589,0.559c2.055,2.49-0.206,8.923,0.646,12.502
			c-1.195,0.305-3.753,0.305-4.945,0c-0.38-3.524,0.791-8.397-0.645-11.011c-1.465,2.442-0.245,7.533-0.646,11.011
			c-1.192,0.305-3.751,0.305-4.943,0C96.914,73.37,96.914,67.958,96.914,62.544z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M45.537,63.852
			c1.867,1.01,2.84,3.203,4.083,5.039c1.712,2.523,3.295,5.078,4.944,7.648c-1.28,1.191-3.131,1.885-4.944,2.618
			c-2.858-4.301-5.663-8.648-8.598-12.879C42.27,65.246,44.054,64.682,45.537,63.852z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M33.927,69.826
			c1.724-1.054,3.056-2.808,6.235-2.057c3.207,3.499,5.577,7.722,8.169,11.758c-1.004,1.306-2.932,1.809-4.516,2.612
			c-2.767-3.259-4.527-7.39-7.523-10.451c-0.015,2.078,1.668,3.896,2.794,5.601c1.191,1.799,2.41,3.578,3.655,5.225
			c-1.188,1.207-3.258,1.649-4.729,2.611c-3.831-5.07-7.2-10.547-10.75-15.86c1.412-0.829,2.94-1.556,4.514-2.239
			C33.003,67.517,33.494,68.646,33.927,69.826z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M76.061,70.943
			c3.153-0.39,7.566,2.283,6.449,6.717c-0.541,2.155-3.864,3.241-6.664,4.665c-2.521,1.284-4.693,3.235-8.167,2.612
			c-3.466-0.617-6.201-5.801-3.225-8.581c0.943-0.887,3.256-1.722,5.374-2.801C72.277,72.307,73.728,71.231,76.061,70.943z
			 M67.248,80.648c4.45-1.18,7.912-3.211,11.178-5.413c-0.045-0.209-0.239-0.289-0.43-0.377
			C74.461,76.832,70.266,78.227,67.248,80.648z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M27.48,73.183
			c0.666,0.019,0.612-0.587,1.504-0.374c0.379,0.728,0.909,1.325,1.289,2.05c0.207,0.68-0.965,0.159-0.859,0.748
			c1.392,2.835,3.438,5.103,4.944,7.839c0.788,0.125,0.825-0.402,1.505-0.373c0.554,0.765,1.292,1.366,1.505,2.427
			c-2.122,0.581-4.516,3.259-6.88,1.68c-2.325-1.555-4.294-7.299-6.018-8.957c-1.648,0.913-1.821-0.968-2.579-1.865
			c-0.188-0.601,0.708-0.256,0.645-0.75c-0.154-0.674-0.706-1.003-0.86-1.677C22.966,72.556,26.891,69.857,27.48,73.183z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M84.016,84.753
			c1.699,0.782,3.325,2.702,2.148,4.852c-3.873,2.234-7.917,4.322-12.253,6.158c-1.227-0.989-1.966-2.398-2.58-3.922
			c3.738-1.728,7.656-3.306,10.75-5.595c-4.042,0.72-7.366,3.682-11.393,5.036c-0.919-1.131-1.58-2.485-2.581-3.547
			c4.323-2.651,9.133-4.883,13.974-7.087c0.467,1.334,1.448,2.224,2.149,3.359C84.407,84.604,84.413,84.393,84.016,84.753z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M61.874,86.246
			c1.218,0,2.436,0,3.655,0c0.377,0.722-0.013,2.04,1.074,1.68c-0.145,0.805,0.491,2.29-0.858,2.051c0,1.989,0,3.981,0,5.972
			c0.054,0.388,0.942,0.05,1.073,0.373c-0.059,0.635,0.166,1.511-0.215,1.867c-6.209,1.346-4.92-3.816-4.944-7.837
			c-0.378-0.605-1.187-0.837-0.859-2.055C61.542,87.947,61.692,87.084,61.874,86.246z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M60.799,93.15
			c-1.434,0-2.868,0-4.3,0c-0.497,0.627-0.076,2.048-0.214,2.985c1.263-0.025,0.219-2.051,0.86-2.614c1.217,0,2.437,0,3.654,0
			c1.768,5.186-6.191,5.858-8.385,3.174c-0.436-1.673,0.006-4.112-0.429-5.787c-0.722,2.112-0.427,5.105-1.076,7.28
			c-1.361,0-2.723,0-4.083,0c-0.742-0.479-0.152-2.109-0.646-2.802c-0.331,0.838-0.354,1.935-0.644,2.802c-1.362,0-2.723,0-4.084,0
			c-0.87-3.228-1.13-6.984-1.72-10.453c0.793-0.298,2.86-0.298,3.653,0c0.104,0.969-0.23,2.316,0.216,2.988
			c0.459-0.784,0.755-1.707,0.645-2.988c0.771-0.386,2.325-0.093,3.439-0.183c0.466,0.839-0.037,2.519,0.43,3.355
			c0.375-1.041,0.309-2.466,0.86-3.355c1.075,0,2.15,0,3.224,0c0.582,0.749-0.727,1.591,0,2.424
			C52.866,85.87,62.935,86.604,60.799,93.15z M56.714,91.096c-0.066-0.562,0.238-1.451-0.43-1.491
			C56.251,89.854,55.604,91.056,56.714,91.096z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M59.079,77.848
			c1.029,1.222,1.914,2.567,2.58,4.105c-0.502,0.308-0.52,1.043-1.505,0.933c-0.635,0.165,0.5,0.278,0.216,0.747
			c-0.851,0.193-1.085,0.924-2.149,0.934c-0.106-0.779-0.728-1.11-1.075-1.681c0.314,1.295-1.093,3.091-3.011,2.426
			c-0.089,0.694-0.881,1.654-2.578,1.493c-0.861-1.864-2.927-2.685-3.224-5.039c0.535-0.28,1.053-0.576,1.72-0.745
			c0.606,0.22,1.146,0.496,1.504,0.933c0.123-1.4,2.108-2.704,3.654-1.679c0.697-0.325-0.558-0.488-0.215-1.117
			C56.549,78.886,56.909,77.578,59.079,77.848z M53.275,84.938c-0.445-0.925-1.008-1.632-1.506-2.052
			C51.593,83.483,52.434,84.622,53.275,84.938z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M74.128,38.474
			c1.311,0.886,1.787,1.738,2.793,3.358c0.51,0.819,1.085,1.605,1.075,2.238c-0.032,2.179-3.911,3.965-5.804,2.614
			c-1.162-0.828-3.338-4.407-3.225-5.599C69.141,39.281,71.147,38.271,74.128,38.474z M72.623,41.646
			c0.349-0.604-0.122-0.978-0.86-1.119C71.895,41.033,72.188,41.401,72.623,41.646z M73.48,43.324
			c0.77,0.389,0.587,1.606,1.721,1.681c0.048-0.939-1.71-1.923-0.215-2.425C74.386,42.15,73.853,42.976,73.48,43.324z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M92.4,46.124
			c0.927,1.727,4.736,5.312,5.373,7.651c0.796,2.923-4.527,5.659-6.879,3.172c0.234-0.667,1.362-0.558,1.72-1.119
			c-0.398-0.57-1.021,0.215-1.72,0c-1.914-1.797-4.341-4.023-4.083-7.093C88.74,47.924,90.577,47.031,92.4,46.124z M93.045,53.214
			c-0.397-1.148-1.36-3.419-3.01-4.104C90.809,50.57,91.719,52.852,93.045,53.214z M93.474,55.455c0.675-0.094,0.841,0.407,1.29,0
			c0.089-0.452-0.393-0.405-0.215-0.934C93.782,54.479,93.425,54.789,93.474,55.455z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M67.032,68.891
			c0.613-0.586,1.82-0.659,2.365-1.309c-0.509-0.427-1.93-0.065-2.795-0.187c-2.713-3.053-5.908-5.693-6.233-10.821
			c0.885-1.057,2.625-2.297,4.729-1.678c0.428-1.932,3.057-1.952,4.299-3.174c0.349-0.053,0.37,0.174,0.645,0.187
			c0.859,1.25,2.221,3.731,3.869,6.157c1.577,2.32,4.205,4.636,4.3,7.091c0.11,2.867-3.407,5.328-6.663,5.786
			C69.237,71.269,67.691,70.524,67.032,68.891z M68.107,60.866c0.652,0.963,1.209,2.285,2.365,2.611
			c-0.215-2.434-2.831-4.375-3.87-6.716c-0.215,0-0.43,0-0.645,0C66.127,58.23,67.199,59.524,68.107,60.866z M70.688,67.025
			c0.853-0.72,1.107,0.124,2.363,0c0.039-0.843-0.211-1.435-0.859-1.682C72.035,65.814,70.77,66.649,70.688,67.025z"/>
		<path fill-rule="evenodd" clip-rule="evenodd" fill="#FFFFFF" stroke="#2980DE" stroke-width="0.5" d="M91.97,83.258
			c2.054-1.035,4.812,0.315,3.868,2.428c0.53-0.73,1.925-0.987,2.795-0.748c2.691,0.744,0.454,4.499-0.859,5.414
			c-2.631,0.384-3.56-1.166-3.439-2.616c-0.783,1.169-2.798,0.578-3.654-0.183c-0.225-0.429-0.136-1.126-0.431-1.495
			c-2.123,1.893-5.732-2.328-2.15-2.612C86.34,80.198,93.34,80.435,91.97,83.258z M91.109,84.938
			c-0.102-0.584,0.58-0.492,0.645-0.93c-0.275-0.097-1.286-0.474-1.29-0.189C90.929,83.979,90.646,84.781,91.109,84.938z"/>
	</g>
</g>
</svg>
